#!/usr/bin/env python3
"""
Test script to verify auto-shutdown persistence behavior
"""

import json
import os

def test_config_persistence():
    """Test that auto-shutdown settings persist in config file"""
    config_file = "config.json"
    
    print("Testing Auto-Shutdown Persistence")
    print("=" * 40)
    
    # Read current config
    try:
        with open(config_file, "r") as f:
            config = json.load(f)
        print("✓ Config file loaded successfully")
    except Exception as e:
        print(f"✗ Error loading config: {e}")
        return False
    
    # Test 1: Check if auto-shutdown parameters exist
    print("\n1. Checking auto-shutdown parameters:")
    if "auto_shutdown_enabled" in config:
        current_enabled = config["auto_shutdown_enabled"]
        print(f"   ✓ auto_shutdown_enabled: {current_enabled}")
    else:
        print("   ✗ auto_shutdown_enabled not found")
        return False
    
    if "auto_shutdown_time" in config:
        current_time = config["auto_shutdown_time"]
        print(f"   ✓ auto_shutdown_time: {current_time} minutes")
    else:
        print("   ✗ auto_shutdown_time not found")
        return False
    
    # Test 2: Simulate enabling auto-shutdown
    print("\n2. Testing persistence by enabling auto-shutdown:")
    test_config = config.copy()
    test_config["auto_shutdown_enabled"] = True
    test_config["auto_shutdown_time"] = 30
    
    # Save test config
    try:
        with open("test_config.json", "w") as f:
            json.dump(test_config, f, indent=2)
        print("   ✓ Test config saved with auto-shutdown enabled")
    except Exception as e:
        print(f"   ✗ Error saving test config: {e}")
        return False
    
    # Read it back
    try:
        with open("test_config.json", "r") as f:
            loaded_config = json.load(f)
        
        if loaded_config["auto_shutdown_enabled"] == True:
            print("   ✓ auto_shutdown_enabled persisted correctly")
        else:
            print("   ✗ auto_shutdown_enabled not persisted")
            return False
            
        if loaded_config["auto_shutdown_time"] == 30:
            print("   ✓ auto_shutdown_time persisted correctly")
        else:
            print("   ✗ auto_shutdown_time not persisted")
            return False
            
    except Exception as e:
        print(f"   ✗ Error reading test config: {e}")
        return False
    
    # Test 3: Simulate disabling auto-shutdown
    print("\n3. Testing persistence by disabling auto-shutdown:")
    test_config["auto_shutdown_enabled"] = False
    
    try:
        with open("test_config.json", "w") as f:
            json.dump(test_config, f, indent=2)
        
        with open("test_config.json", "r") as f:
            loaded_config = json.load(f)
        
        if loaded_config["auto_shutdown_enabled"] == False:
            print("   ✓ auto_shutdown_enabled disabled and persisted correctly")
        else:
            print("   ✗ auto_shutdown_enabled disable not persisted")
            return False
            
    except Exception as e:
        print(f"   ✗ Error testing disable persistence: {e}")
        return False
    
    # Cleanup
    try:
        os.remove("test_config.json")
        print("   ✓ Test file cleaned up")
    except:
        pass
    
    print("\n" + "=" * 40)
    print("✅ All persistence tests passed!")
    print("\nBehavior Summary:")
    print("• When you enable auto-shutdown, it saves to config.json")
    print("• When you restart the app, it reads the saved setting")
    print("• If enabled=True, it automatically schedules shutdown")
    print("• If enabled=False, it remains disabled")
    print("• Settings persist until manually changed")
    
    return True

def show_current_config():
    """Show current auto-shutdown configuration"""
    try:
        with open("config.json", "r") as f:
            config = json.load(f)
        
        print("\nCurrent Auto-Shutdown Configuration:")
        print("-" * 35)
        enabled = config.get("auto_shutdown_enabled", False)
        time_mins = config.get("auto_shutdown_time", 60)
        
        print(f"Enabled: {enabled}")
        print(f"Time: {time_mins} minutes")
        
        if enabled:
            print("🟢 Auto-shutdown will start when you open the app")
        else:
            print("🔴 Auto-shutdown is disabled")
            
    except Exception as e:
        print(f"Error reading config: {e}")

if __name__ == "__main__":
    test_config_persistence()
    show_current_config()
