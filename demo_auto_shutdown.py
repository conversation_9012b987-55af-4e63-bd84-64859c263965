#!/usr/bin/env python3
"""
Demo script to show how to enable auto-shutdown programmatically
This simulates what happens when a user enables auto-shutdown in the GUI
"""

import json

def enable_auto_shutdown(minutes=60):
    """Enable auto-shutdown for specified minutes"""
    try:
        # Load current config
        with open("config.json", "r") as f:
            config = json.load(f)
        
        # Enable auto-shutdown
        config["auto_shutdown_enabled"] = True
        config["auto_shutdown_time"] = minutes
        
        # Save config
        with open("config.json", "w") as f:
            json.dump(config, f, indent=2)
        
        print(f"✅ Auto-shutdown enabled for {minutes} minutes")
        print("📝 Setting saved to config.json")
        print("🔄 Next time you open the app, it will automatically schedule shutdown")
        
        return True
        
    except Exception as e:
        print(f"❌ Error enabling auto-shutdown: {e}")
        return False

def disable_auto_shutdown():
    """Disable auto-shutdown"""
    try:
        # Load current config
        with open("config.json", "r") as f:
            config = json.load(f)
        
        # Disable auto-shutdown
        config["auto_shutdown_enabled"] = False
        
        # Save config
        with open("config.json", "w") as f:
            json.dump(config, f, indent=2)
        
        print("✅ Auto-shutdown disabled")
        print("📝 Setting saved to config.json")
        print("🔄 App will not schedule shutdown on next startup")
        
        return True
        
    except Exception as e:
        print(f"❌ Error disabling auto-shutdown: {e}")
        return False

def show_status():
    """Show current auto-shutdown status"""
    try:
        with open("config.json", "r") as f:
            config = json.load(f)
        
        enabled = config.get("auto_shutdown_enabled", False)
        minutes = config.get("auto_shutdown_time", 60)
        
        print("\n📊 Current Auto-Shutdown Status:")
        print("=" * 35)
        
        if enabled:
            print(f"🟢 Status: ENABLED")
            print(f"⏰ Time: {minutes} minutes")
            print(f"🚀 Will activate on next app startup")
        else:
            print(f"🔴 Status: DISABLED")
            print(f"⏰ Time: {minutes} minutes (saved for next use)")
            print(f"💤 Will NOT activate on app startup")
            
    except Exception as e:
        print(f"❌ Error reading status: {e}")

def main():
    """Demo the auto-shutdown feature"""
    print("🖥️  Auto-Shutdown Feature Demo")
    print("=" * 40)
    
    # Show current status
    show_status()
    
    print("\n🎯 Demo Actions:")
    print("1. Enable auto-shutdown for 5 minutes")
    print("2. Show status")
    print("3. Disable auto-shutdown")
    print("4. Show final status")
    
    input("\nPress Enter to start demo...")
    
    # Step 1: Enable
    print("\n1️⃣ Enabling auto-shutdown for 5 minutes...")
    enable_auto_shutdown(5)
    
    # Step 2: Show status
    print("\n2️⃣ Current status after enabling:")
    show_status()
    
    input("\nPress Enter to continue...")
    
    # Step 3: Disable
    print("\n3️⃣ Disabling auto-shutdown...")
    disable_auto_shutdown()
    
    # Step 4: Final status
    print("\n4️⃣ Final status after disabling:")
    show_status()
    
    print("\n✨ Demo Complete!")
    print("\n📋 Summary:")
    print("• Settings are saved to config.json immediately")
    print("• When enabled, the app will schedule shutdown on startup")
    print("• When disabled, no shutdown will be scheduled")
    print("• Settings persist between app sessions")

if __name__ == "__main__":
    main()
