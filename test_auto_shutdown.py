#!/usr/bin/env python3
"""
Test script for auto-shutdown functionality
This script tests the auto-shutdown feature without actually shutting down the PC
"""

import json
import time
from datetime import datetime

def test_config_loading():
    """Test that the config file loads the new auto-shutdown parameters"""
    try:
        with open("config.json", "r") as f:
            config = json.load(f)
        
        print("✓ Config file loaded successfully")
        
        # Check if auto-shutdown parameters exist
        if "auto_shutdown_enabled" in config:
            print(f"✓ auto_shutdown_enabled found: {config['auto_shutdown_enabled']}")
        else:
            print("✗ auto_shutdown_enabled not found in config")
            
        if "auto_shutdown_time" in config:
            print(f"✓ auto_shutdown_time found: {config['auto_shutdown_time']} minutes")
        else:
            print("✗ auto_shutdown_time not found in config")
            
        return True
        
    except Exception as e:
        print(f"✗ Error loading config: {e}")
        return False

def test_shutdown_command():
    """Test the shutdown command format (without actually executing it)"""
    try:
        import subprocess
        
        # Test command format (dry run)
        cmd = ["shutdown", "/s", "/t", "30", "/c", "Auto shutdown initiated by application"]
        print(f"✓ Shutdown command format: {' '.join(cmd)}")
        
        # Note: We don't actually run this command to avoid shutting down the PC
        print("✓ Shutdown command format is valid")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing shutdown command: {e}")
        return False

def test_time_calculations():
    """Test time calculation functions"""
    try:
        # Simulate auto shutdown timer calculations
        shutdown_minutes = 60
        shutdown_ms = shutdown_minutes * 60 * 1000
        
        print(f"✓ Time conversion: {shutdown_minutes} minutes = {shutdown_ms} milliseconds")
        
        # Test remaining time calculation
        start_time = time.time()
        elapsed_time = 5  # Simulate 5 seconds elapsed
        remaining_time = (shutdown_minutes * 60) - elapsed_time
        remaining_minutes = max(0, remaining_time / 60)
        
        print(f"✓ Remaining time calculation: {remaining_minutes:.1f} minutes remaining")
        
        return True
        
    except Exception as e:
        print(f"✗ Error in time calculations: {e}")
        return False

def main():
    """Run all tests"""
    print("Testing Auto-Shutdown Functionality")
    print("=" * 40)
    
    tests = [
        ("Config Loading", test_config_loading),
        ("Shutdown Command", test_shutdown_command),
        ("Time Calculations", test_time_calculations)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\nTesting {test_name}:")
        if test_func():
            passed += 1
            print(f"✓ {test_name} PASSED")
        else:
            print(f"✗ {test_name} FAILED")
    
    print("\n" + "=" * 40)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Auto-shutdown functionality is ready.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")

if __name__ == "__main__":
    main()
